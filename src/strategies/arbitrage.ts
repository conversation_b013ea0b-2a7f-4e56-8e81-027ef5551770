import { ethers } from 'ethers';
import { ArbitrageRoute, Pool, Token, MEVOpportunity } from '../types';
import { PoolManager } from '../dex/pools';
import { GasOptimizer } from '../gas/optimizer';
import { CalldataEncoder } from '../calldata/encoder';
import { BundleSimulator } from '../simulation/simulator';
import { config, COMMON_TOKENS } from '../config';
import { logger } from '../utils/logger';
import { WorkerManager } from '../workers/worker-manager';
import { getPerformanceConfig } from '../config/performance';

export class ArbitrageStrategy {
  private poolManager: PoolManager;
  private gasOptimizer: GasOptimizer;
  private encoder: CalldataEncoder;
  private simulator: BundleSimulator;
  private wallet: ethers.Wallet;
  private workerManager: WorkerManager | null = null;
  private readonly MIN_PROFIT_THRESHOLD = 0.001; // 0.1% minimum profit (lowered for testing)
  private useWorkers: boolean;

  constructor() {
    this.poolManager = new PoolManager();
    this.gasOptimizer = new GasOptimizer();
    this.encoder = new CalldataEncoder();
    this.simulator = new BundleSimulator();
    this.wallet = new ethers.Wallet(config.privateKey);

    // Initialize worker manager based on performance config
    const perfConfig = getPerformanceConfig();
    this.useWorkers = perfConfig.workerThreads.enabled;

    if (this.useWorkers) {
      this.workerManager = new WorkerManager({
        maxWorkers: perfConfig.workerThreads.maxWorkers,
        taskTimeout: perfConfig.workerThreads.taskTimeout,
        enableLoadBalancing: true
      });

      // Initialize worker pool
      this.initializeWorkers();
    }
  }

  private async initializeWorkers(): Promise<void> {
    if (this.useWorkers && this.workerManager) {
      try {
        await this.workerManager.initialize();
        logger.info(`ArbitrageStrategy: Worker pool initialized with ${this.workerManager.getWorkerStats().length} workers`);
      } catch (error) {
        logger.error('Failed to initialize worker pool, falling back to single-threaded mode:', error);
        this.useWorkers = false;
        this.workerManager = null;
      }
    }
  }

  async scanForArbitrageOpportunities(): Promise<ArbitrageRoute[]> {
    try {
      logger.debug('Starting arbitrage scan', {
        tokenCount: COMMON_TOKENS.length,
        useWorkers: this.useWorkers,
        totalPairs: (COMMON_TOKENS.length * (COMMON_TOKENS.length - 1)) / 2
      });

      // Use worker-based scanning if enabled and available
      if (this.useWorkers && this.workerManager) {
        return await this.scanWithWorkers();
      } else {
        return await this.scanSingleThreaded();
      }
    } catch (error) {
      logger.logError(error as Error, 'ArbitrageStrategy.scanForArbitrageOpportunities');

      // Fallback to single-threaded if workers fail
      if (this.useWorkers) {
        logger.warn('Worker-based scanning failed, falling back to single-threaded mode');
        this.useWorkers = false;
        return await this.scanSingleThreaded();
      }

      return [];
    }
  }

  private async scanWithWorkers(): Promise<ArbitrageRoute[]> {
    const startTime = Date.now();

    try {
      // Generate all token pairs
      const tokenPairs: Array<{ token0: Token; token1: Token }> = [];
      for (let i = 0; i < COMMON_TOKENS.length; i++) {
        for (let j = i + 1; j < COMMON_TOKENS.length; j++) {
          tokenPairs.push({
            token0: COMMON_TOKENS[i],
            token1: COMMON_TOKENS[j]
          });
        }
      }

      // Get current gas price for workers
      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
      const gasPrice = gasStrategy.maxFeePerGas.toString();

      // Process token pairs using worker pool
      const result = await this.workerManager.processArbitrageScan(
        tokenPairs,
        this.MIN_PROFIT_THRESHOLD,
        gasPrice
      );

      const processingTime = Date.now() - startTime;

      logger.info(`Worker-based arbitrage scan complete:`, {
        opportunities: result.opportunities.length,
        processedPairs: result.processedPairs,
        profitableCount: result.profitableCount,
        processingTime: `${processingTime}ms`,
        avgTimePerPair: `${(processingTime / result.processedPairs).toFixed(2)}ms`
      });

      if (result.opportunities.length === 0) {
        logger.warn('No arbitrage opportunities found - this may indicate:');
        logger.warn('• Pool loading errors (check PoolManager logs)');
        logger.warn('• No price differences between DEXs');
        logger.warn('• Insufficient liquidity in pools');
        logger.warn('• Network connectivity issues');
      }

      return result.opportunities;
    } catch (error) {
      logger.error('Error in worker-based arbitrage scan:', error);
      throw error;
    }
  }

  private async scanSingleThreaded(): Promise<ArbitrageRoute[]> {
    const opportunities: ArbitrageRoute[] = [];
    const startTime = Date.now();

    try {
      // Scan common token pairs across different protocols
      for (let i = 0; i < COMMON_TOKENS.length; i++) {
        for (let j = i + 1; j < COMMON_TOKENS.length; j++) {
          const token0 = COMMON_TOKENS[i];
          const token1 = COMMON_TOKENS[j];

          logger.debug('Scanning token pair', {
            token0: token0.symbol,
            token1: token1.symbol
          });

          // Check arbitrage between Uniswap V2 and V3
          const v2v3Arbitrage = await this.findV2V3Arbitrage(token0, token1);
          if (v2v3Arbitrage) {
            opportunities.push(v2v3Arbitrage);
            logger.debug('V2/V3 arbitrage found', {
              token0: token0.symbol,
              token1: token1.symbol,
              profit: ethers.formatEther(v2v3Arbitrage.expectedProfit)
            });
          }

          // Check triangular arbitrage opportunities
          const triangularArbitrage = await this.findTriangularArbitrage(token0, token1);
          if (triangularArbitrage.length > 0) {
            opportunities.push(...triangularArbitrage);
            logger.debug('Triangular arbitrage found', {
              count: triangularArbitrage.length,
              token0: token0.symbol,
              token1: token1.symbol
            });
          }
        }
      }

      // Sort by expected profit
      opportunities.sort((a, b) =>
        Number(BigInt(b.expectedProfit.toString()) - BigInt(a.expectedProfit.toString()))
      );

      const processingTime = Date.now() - startTime;
      const totalPairs = (COMMON_TOKENS.length * (COMMON_TOKENS.length - 1)) / 2;

      logger.info(`Single-threaded arbitrage scan complete:`, {
        opportunities: opportunities.length,
        processedPairs: totalPairs,
        processingTime: `${processingTime}ms`,
        avgTimePerPair: `${(processingTime / totalPairs).toFixed(2)}ms`
      });

      if (opportunities.length === 0) {
        logger.warn('No arbitrage opportunities found - this may indicate:');
        logger.warn('• Pool loading errors (check PoolManager logs)');
        logger.warn('• No price differences between DEXs');
        logger.warn('• Insufficient liquidity in pools');
        logger.warn('• Network connectivity issues');
      }

      return opportunities.slice(0, 10); // Return top 10
    } catch (error) {
      logger.logError(error as Error, 'ArbitrageStrategy.scanSingleThreaded');
      return [];
    }
  }

  private async findV2V3Arbitrage(token0: Token, token1: Token): Promise<ArbitrageRoute | null> {
    try {
      logger.debug('Finding V2/V3 arbitrage', {
        token0: token0.symbol,
        token1: token1.symbol
      });

      // Get pools from both V2 and V3
      const v2Pool = await this.poolManager.getPool(token0.address, token1.address, 'uniswap-v2');
      const v3Pool = await this.poolManager.getPool(token0.address, token1.address, 'uniswap-v3', 3000);

      logger.debug('Pool lookup results', {
        v2Pool: v2Pool ? 'found' : 'not found',
        v3Pool: v3Pool ? 'found' : 'not found',
        v2Address: v2Pool?.address,
        v3Address: v3Pool?.address
      });

      if (!v2Pool || !v3Pool) {
        logger.debug('Missing pools for arbitrage', {
          token0: token0.symbol,
          token1: token1.symbol,
          v2Available: !!v2Pool,
          v3Available: !!v3Pool
        });
        return null;
      }

      // Calculate prices on both pools
      const v2Price = this.calculatePoolPrice(v2Pool, token0, token1);
      const v3Price = this.calculatePoolPrice(v3Pool, token0, token1);

      logger.debug('Price calculation results', {
        v2Price,
        v3Price,
        token0: token0.symbol,
        token1: token1.symbol
      });

      if (!v2Price || !v3Price || v2Price <= 0 || v3Price <= 0) {
        logger.debug('Invalid prices calculated', { v2Price, v3Price });
        return null;
      }

      // Check for price difference
      const priceDifference = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);

      logger.debug('Price difference analysis', {
        priceDifference: `${(priceDifference * 100).toFixed(2)}%`,
        threshold: `${(this.MIN_PROFIT_THRESHOLD * 100).toFixed(2)}%`,
        profitable: priceDifference >= this.MIN_PROFIT_THRESHOLD
      });

      if (priceDifference < this.MIN_PROFIT_THRESHOLD) {
        return null;
      }

      // Determine direction (buy low, sell high)
      const buyPool = v2Price < v3Price ? v2Pool : v3Pool;
      const sellPool = v2Price < v3Price ? v3Pool : v2Pool;
      const buyToken = token0;
      const sellToken = token1;

      logger.debug('Arbitrage direction', {
        buyFrom: v2Price < v3Price ? 'V2' : 'V3',
        sellTo: v2Price < v3Price ? 'V3' : 'V2'
      });

      // Calculate optimal amount
      const optimalAmount = await this.calculateOptimalArbitrageAmount(buyPool, sellPool, buyToken, sellToken);

      if (optimalAmount === BigInt(0)) {
        logger.debug('Optimal amount is zero');
        return null;
      }

      // Estimate gas costs
      const gasEstimate = await this.estimateArbitrageGasCost(buyPool, sellPool, optimalAmount);

      // Calculate expected profit
      const expectedProfit = await this.calculateArbitrageProfit(
        buyPool, sellPool, buyToken, sellToken, optimalAmount, gasEstimate
      );

      logger.debug('Profit calculation', {
        optimalAmount: ethers.formatUnits(optimalAmount, buyToken.decimals),
        gasEstimate: ethers.formatEther(gasEstimate),
        expectedProfit: ethers.formatEther(expectedProfit),
        profitable: BigInt(expectedProfit.toString()) > BigInt(0)
      });

      // Use a more reasonable minimum profit threshold (0.001 ETH instead of 0.01 ETH)
      const minProfitWei = ethers.parseEther('0.001'); // 0.001 ETH minimum
      if (BigInt(expectedProfit.toString()) <= minProfitWei) {
        logger.debug('Profit below minimum threshold', {
          expectedProfit: ethers.formatEther(expectedProfit),
          minProfit: ethers.formatEther(minProfitWei)
        });
        return null;
      }

      return {
        pools: [buyPool, sellPool],
        tokens: [buyToken, sellToken],
        expectedProfit,
        gasEstimate,
        confidence: this.calculateArbitrageConfidence(priceDifference, expectedProfit)
      };
    } catch (error) {
      logger.logError(error as Error, `V2/V3 arbitrage error for ${token0.symbol}/${token1.symbol}`);
      return null;
    }
  }

  private async findTriangularArbitrage(token0: Token, token1: Token): Promise<ArbitrageRoute[]> {
    const routes: ArbitrageRoute[] = [];

    try {
      // Find triangular arbitrage: token0 -> WETH -> token1 -> token0
      const weth = COMMON_TOKENS.find(t => t.symbol === 'WETH');
      if (!weth || weth.address === token0.address || weth.address === token1.address) {
        return routes;
      }

      // Get all required pools
      const pool1 = await this.poolManager.getPool(token0.address, weth.address, 'uniswap-v2');
      const pool2 = await this.poolManager.getPool(weth.address, token1.address, 'uniswap-v2');
      const pool3 = await this.poolManager.getPool(token1.address, token0.address, 'uniswap-v2');

      if (!pool1 || !pool2 || !pool3) {
        return routes;
      }

      // Calculate if triangular arbitrage is profitable
      const testAmount = ethers.parseUnits('1', token0.decimals);
      const step1Out = await this.calculateAmountOut(pool1, testAmount, token0, weth);
      const step2Out = await this.calculateAmountOut(pool2, step1Out, weth, token1);
      const step3Out = await this.calculateAmountOut(pool3, step2Out, token1, token0);

      const profit = BigInt(step3Out.toString()) - testAmount;
      const profitPercentage = Number(profit * BigInt(10000) / testAmount) / 100;

      if (profitPercentage > this.MIN_PROFIT_THRESHOLD) {
        const optimalAmount = await this.calculateOptimalTriangularAmount(
          [pool1, pool2, pool3],
          [token0, weth, token1, token0]
        );

        const gasEstimate = await this.estimateTriangularGasCost([pool1, pool2, pool3], optimalAmount);
        const expectedProfit = await this.calculateTriangularProfit(
          [pool1, pool2, pool3],
          [token0, weth, token1, token0],
          optimalAmount,
          gasEstimate
        );

        if (BigInt(expectedProfit.toString()) > BigInt(0)) {
          routes.push({
            pools: [pool1, pool2, pool3],
            tokens: [token0, weth, token1, token0],
            expectedProfit,
            gasEstimate,
            confidence: this.calculateArbitrageConfidence(profitPercentage, expectedProfit)
          });
        }
      }

      return routes;
    } catch (error) {
      logger.debug('Error finding triangular arbitrage', { error: (error as Error).message });
      return routes;
    }
  }

  private calculatePoolPrice(pool: Pool, token0: Token, token1: Token): number | null {
    try {
      if (pool.protocol === 'uniswap-v2' && pool.reserves) {
        // For V2, we need to check the actual token ordering in the pool
        const actualToken0 = pool.token0;
        const actualToken1 = pool.token1;

        // Determine which reserve corresponds to which token
        const isToken0First = actualToken0.address.toLowerCase() === token0.address.toLowerCase();

        const token0Reserve = isToken0First ? pool.reserves.reserve0 : pool.reserves.reserve1;
        const token1Reserve = isToken0First ? pool.reserves.reserve1 : pool.reserves.reserve0;

        const reserve0 = Number(ethers.formatUnits(token0Reserve, token0.decimals));
        const reserve1 = Number(ethers.formatUnits(token1Reserve, token1.decimals));

        if (reserve0 === 0) return null;

        const price = reserve1 / reserve0; // Price of token0 in terms of token1

        logger.debug('V2 price calculation', {
          token0: token0.symbol,
          token1: token1.symbol,
          reserve0,
          reserve1,
          price,
          isToken0First
        });

        return price;
      } else if (pool.protocol === 'uniswap-v3' && pool.tick !== undefined && pool.tick !== null) {
        // For V3, we need to handle token ordering and tick calculation properly
        const actualToken0 = pool.token0;
        const actualToken1 = pool.token1;

        // Check if our token0 is the pool's token0
        const isToken0First = actualToken0.address.toLowerCase() === token0.address.toLowerCase();

        const tickNumber = Number(pool.tick);
        let rawPrice = Math.pow(1.0001, tickNumber);

        // If token order is reversed, invert the price
        if (!isToken0First) {
          rawPrice = 1 / rawPrice;
        }

        // Adjust for token decimals
        // We want price of token0 in terms of token1, so we need: token0.decimals - token1.decimals
        const decimalsAdjustment = Math.pow(10, token0.decimals - token1.decimals);
        const price = rawPrice * decimalsAdjustment;

        logger.debug('V3 price calculation', {
          token0: token0.symbol,
          token1: token1.symbol,
          tick: tickNumber,
          rawPrice,
          decimalsAdjustment,
          price,
          isToken0First
        });

        return price;
      } else if (pool.protocol === 'uniswap-v3' && pool.sqrtPriceX96) {
        // Fallback to sqrtPriceX96 if tick is not available
        const actualToken0 = pool.token0;
        const isToken0First = actualToken0.address.toLowerCase() === token0.address.toLowerCase();

        const sqrtPrice = Number(pool.sqrtPriceX96) / (2 ** 96);
        let price = sqrtPrice ** 2;

        // If token order is reversed, invert the price
        if (!isToken0First) {
          price = 1 / price;
        }

        // Adjust for token decimals
        // We want price of token0 in terms of token1, so we need: token0.decimals - token1.decimals
        const decimalsAdjustment = Math.pow(10, token0.decimals - token1.decimals);
        price = price * decimalsAdjustment;

        logger.debug('V3 sqrtPrice calculation', {
          token0: token0.symbol,
          token1: token1.symbol,
          sqrtPriceX96: pool.sqrtPriceX96.toString(),
          price,
          isToken0First
        });

        return price;
      }

      logger.debug('Unable to calculate price', {
        protocol: pool.protocol,
        hasReserves: !!pool.reserves,
        hasTick: pool.tick !== undefined,
        hasSqrtPrice: !!pool.sqrtPriceX96
      });

      return null;
    } catch (error) {
      logger.logError(error as Error, `calculatePoolPrice for ${token0.symbol}/${token1.symbol}`);
      return null;
    }
  }

  private async calculateOptimalArbitrageAmount(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token
  ): Promise<ethers.BigNumberish> {
    // This would use calculus to find the optimal amount that maximizes profit
    // For now, use a simplified approach
    const maxAmount = ethers.parseUnits('10', buyToken.decimals); // Max 10 tokens
    let optimalAmount = BigInt(0);
    let maxProfit = BigInt(0);

    // Test different amounts to find optimal
    for (let i = 1; i <= 10; i++) {
      const testAmount = (maxAmount * BigInt(i)) / BigInt(10);

      const buyAmountOut = await this.calculateAmountOut(buyPool, testAmount, buyToken, sellToken);
      const sellAmountOut = await this.calculateAmountOut(sellPool, buyAmountOut, sellToken, buyToken);

      const profit = BigInt(sellAmountOut.toString()) - testAmount;

      if (profit > maxProfit) {
        maxProfit = profit;
        optimalAmount = testAmount;
      }
    }

    return optimalAmount;
  }

  private async calculateAmountOut(
    pool: Pool,
    amountIn: ethers.BigNumberish,
    tokenIn: Token,
    tokenOut: Token
  ): Promise<ethers.BigNumberish> {
    // Use the same calculation as in PoolManager
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, tokenIn.decimals) : BigInt(amountIn.toString());
      const reserve0 = BigInt(pool.reserves.reserve0.toString());
      const reserve1 = BigInt(pool.reserves.reserve1.toString());

      // Determine which reserve corresponds to which token
      const isToken0 = pool.token0.address.toLowerCase() === tokenIn.address.toLowerCase();
      const reserveIn = isToken0 ? reserve0 : reserve1;
      const reserveOut = isToken0 ? reserve1 : reserve0;

      // Uniswap V2 formula with 0.3% fee
      const amountInWithFee = amountInBN * BigInt(997);
      const numerator = amountInWithFee * reserveOut;
      const denominator = reserveIn * BigInt(1000) + amountInWithFee;

      return numerator / denominator;
    }

    // Simplified for V3
    return typeof amountIn === 'string' ? ethers.parseUnits(amountIn, tokenOut.decimals) : amountIn;
  }

  private async estimateArbitrageGasCost(
    buyPool: Pool,
    sellPool: Pool,
    amount: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    // Estimate gas for two swaps
    const gasPerSwap = await this.gasOptimizer.estimateGasForTransaction(
      buyPool.address,
      '0x' // Placeholder data
    );

    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return BigInt(gasPerSwap.toString()) * BigInt(2) * BigInt(gasStrategy.maxFeePerGas.toString());
  }

  private async calculateArbitrageProfit(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token,
    amount: ethers.BigNumberish,
    gasCost: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    const buyAmountOut = await this.calculateAmountOut(buyPool, amount, buyToken, sellToken);
    const sellAmountOut = await this.calculateAmountOut(sellPool, buyAmountOut, sellToken, buyToken);

    const grossProfit = BigInt(sellAmountOut.toString()) - BigInt(amount.toString());
    return grossProfit - BigInt(gasCost.toString());
  }

  private calculateArbitrageConfidence(profitPercentage: number, expectedProfit: ethers.BigNumberish): number {
    let confidence = 0;

    // Profit percentage factor
    confidence += Math.min(profitPercentage * 20, 50); // Max 50 points

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 25, 30); // Max 30 points

    // Arbitrage is generally lower risk than sandwich
    confidence += 20; // Base confidence for arbitrage

    return Math.min(confidence, 100);
  }

  private async calculateOptimalTriangularAmount(pools: Pool[], tokens: Token[]): Promise<ethers.BigNumberish> {
    // Simplified calculation for triangular arbitrage
    return ethers.parseUnits('1', tokens[0].decimals);
  }

  private async estimateTriangularGasCost(pools: Pool[], amount: ethers.BigNumberish): Promise<ethers.BigNumberish> {
    const gasPerSwap = await this.gasOptimizer.estimateGasForTransaction(
      pools[0].address,
      '0x' // Placeholder data
    );

    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return BigInt(gasPerSwap.toString()) * BigInt(3) * BigInt(gasStrategy.maxFeePerGas.toString()); // Three swaps
  }

  private async calculateTriangularProfit(
    pools: Pool[],
    tokens: Token[],
    amount: ethers.BigNumberish,
    gasCost: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    let currentAmount = amount;

    // Execute the triangular path
    for (let i = 0; i < pools.length; i++) {
      currentAmount = await this.calculateAmountOut(
        pools[i],
        currentAmount,
        tokens[i],
        tokens[i + 1]
      );
    }

    const grossProfit = BigInt(currentAmount.toString()) - BigInt(amount.toString());
    return grossProfit - BigInt(gasCost.toString());
  }

  async executeArbitrage(route: ArbitrageRoute): Promise<boolean> {
    try {
      // Create transactions for the arbitrage
      const transactions = [];

      for (let i = 0; i < route.pools.length; i++) {
        const pool = route.pools[i];
        const tokenIn = route.tokens[i];
        const tokenOut = route.tokens[i + 1];

        const swapData = this.encoder.encodeArbitrageSwap(
          tokenIn,
          tokenOut,
          i === 0 ? route.expectedProfit : BigInt(0), // Only first swap has input amount
          pool.protocol as 'uniswap-v2' | 'uniswap-v3',
          this.wallet.address,
          pool.fee
        );

        const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
        const nonce = await this.wallet.getNonce() + i;

        transactions.push({
          hash: '',
          from: this.wallet.address,
          to: pool.address,
          value: BigInt(0),
          gasPrice: gasStrategy.maxFeePerGas,
          gasLimit: await this.gasOptimizer.estimateGasForTransaction(pool.address, swapData),
          data: swapData,
          nonce,
          maxFeePerGas: gasStrategy.maxFeePerGas,
          maxPriorityFeePerGas: gasStrategy.priorityFee
        });
      }

      // Simulate the arbitrage
      const simulationResult = await this.simulator.simulateArbitrage(transactions);

      if (!simulationResult.success) {
        logger.warn('Arbitrage simulation failed', { error: simulationResult.error });
        return false;
      }

      if (config.dryRun) {
        logger.info('DRY RUN: Would execute arbitrage', {
          estimatedProfit: ethers.formatEther(simulationResult.profit),
          gasUsed: simulationResult.gasUsed.toString(),
          pools: route.pools.length
        });
        return true;
      }

      // Execute the arbitrage
      logger.info('Executing arbitrage', {
        pools: route.pools.length,
        expectedProfit: ethers.formatEther(route.expectedProfit)
      });

      return true;
    } catch (error) {
      logger.logError(error as Error, 'ArbitrageStrategy.executeArbitrage');
      return false;
    }
  }

  async shutdown(): Promise<void> {
    if (this.useWorkers && this.workerManager) {
      logger.info('Shutting down ArbitrageStrategy worker pool...');
      await this.workerManager.shutdown();
    }
  }

  getWorkerStats() {
    if (this.useWorkers && this.workerManager) {
      return this.workerManager.getWorkerStats();
    }
    return [];
  }

  isUsingWorkers(): boolean {
    return this.useWorkers;
  }
}
