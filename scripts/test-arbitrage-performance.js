#!/usr/bin/env node

/**
 * Performance test script for arbitrage strategy
 * Compares single-threaded vs multi-threaded performance
 */

const { ArbitrageStrategy } = require('../dist/strategies/arbitrage');
const { performanceMonitor } = require('../dist/utils/performance-monitor');
const { logger } = require('../dist/utils/logger');

async function testArbitragePerformance() {
  console.log('🚀 Starting Arbitrage Performance Test\n');

  try {
    // Initialize arbitrage strategy
    const arbitrageStrategy = new ArbitrageStrategy();
    
    // Wait for worker initialization
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('📊 Testing Configuration:');
    console.log(`   Workers Enabled: ${arbitrageStrategy.isUsingWorkers()}`);
    console.log(`   Worker Count: ${arbitrageStrategy.getWorkerStats().length}`);
    console.log('');

    // Start performance monitoring
    performanceMonitor.start(1000);

    // Test 1: Single scan
    console.log('🔍 Test 1: Single Arbitrage Scan');
    console.log('─'.repeat(40));
    
    const startTime = Date.now();
    const opportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
    const scanTime = Date.now() - startTime;

    console.log(`   Scan Time: ${scanTime}ms`);
    console.log(`   Opportunities Found: ${opportunities.length}`);
    console.log(`   Using Workers: ${arbitrageStrategy.isUsingWorkers()}`);
    console.log('');

    // Test 2: Multiple scans to test sustained performance
    console.log('🔄 Test 2: Multiple Scans (5 iterations)');
    console.log('─'.repeat(40));

    const scanTimes = [];
    for (let i = 0; i < 5; i++) {
      const iterStartTime = Date.now();
      const iterOpportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
      const iterScanTime = Date.now() - iterStartTime;
      
      scanTimes.push(iterScanTime);
      console.log(`   Iteration ${i + 1}: ${iterScanTime}ms (${iterOpportunities.length} opportunities)`);
      
      // Small delay between scans
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    const avgScanTime = scanTimes.reduce((sum, time) => sum + time, 0) / scanTimes.length;
    const minScanTime = Math.min(...scanTimes);
    const maxScanTime = Math.max(...scanTimes);

    console.log('');
    console.log('📈 Performance Summary:');
    console.log('─'.repeat(40));
    console.log(`   Average Scan Time: ${avgScanTime.toFixed(2)}ms`);
    console.log(`   Fastest Scan: ${minScanTime}ms`);
    console.log(`   Slowest Scan: ${maxScanTime}ms`);
    console.log(`   Performance Variance: ${((maxScanTime - minScanTime) / avgScanTime * 100).toFixed(1)}%`);

    // Get performance metrics
    const metrics = performanceMonitor.getLatestMetrics();
    if (metrics) {
      console.log('');
      console.log('💻 System Performance:');
      console.log('─'.repeat(40));
      console.log(`   CPU Usage: ${metrics.cpu.usage.toFixed(1)}%`);
      console.log(`   Memory Usage: ${metrics.memory.percentage.toFixed(1)}%`);
      console.log(`   Event Loop Lag: ${metrics.eventLoop.lag.toFixed(2)}ms`);

      if (metrics.workers) {
        console.log(`   Active Workers: ${metrics.workers.active}/${metrics.workers.total}`);
        console.log(`   Tasks Processed: ${metrics.workers.tasksProcessed}`);
        console.log(`   Avg Processing Time: ${metrics.workers.averageProcessingTime.toFixed(2)}ms`);
      }
    }

    // Worker statistics
    if (arbitrageStrategy.isUsingWorkers()) {
      const workerStats = arbitrageStrategy.getWorkerStats();
      console.log('');
      console.log('👷 Worker Statistics:');
      console.log('─'.repeat(40));
      
      workerStats.forEach((worker, index) => {
        console.log(`   Worker ${worker.workerId}:`);
        console.log(`     Tasks Processed: ${worker.tasksProcessed}`);
        console.log(`     Avg Time: ${worker.averageProcessingTime.toFixed(2)}ms`);
        console.log(`     Errors: ${worker.errorCount}`);
        console.log(`     Status: ${worker.isActive ? 'Active' : 'Inactive'}`);
      });
    }

    // Performance recommendations
    console.log('');
    console.log('💡 Performance Recommendations:');
    console.log('─'.repeat(40));

    if (avgScanTime > 5000) {
      console.log('   ⚠️  Scan times are high (>5s). Consider:');
      console.log('      • Reducing token pairs in configuration');
      console.log('      • Enabling worker threads if disabled');
      console.log('      • Increasing worker count');
    } else if (avgScanTime > 2000) {
      console.log('   ⚠️  Scan times are moderate (>2s). Consider:');
      console.log('      • Optimizing RPC provider performance');
      console.log('      • Enabling more worker threads');
    } else {
      console.log('   ✅ Scan times are optimal (<2s)');
    }

    if (metrics && metrics.cpu.usage > 80) {
      console.log('   ⚠️  High CPU usage detected. Consider:');
      console.log('      • Reducing worker count');
      console.log('      • Increasing scan intervals');
      console.log('      • Using low resource performance mode');
    }

    if (metrics && metrics.memory.percentage > 85) {
      console.log('   ⚠️  High memory usage detected. Consider:');
      console.log('      • Reducing log buffer sizes');
      console.log('      • Enabling garbage collection');
      console.log('      • Reducing transaction history');
    }

    // Cleanup
    console.log('');
    console.log('🧹 Cleaning up...');
    await arbitrageStrategy.shutdown();
    performanceMonitor.stop();

    console.log('✅ Performance test completed successfully!');

  } catch (error) {
    console.error('❌ Performance test failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down performance test...');
  performanceMonitor.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Shutting down performance test...');
  performanceMonitor.stop();
  process.exit(0);
});

// Run the test
if (require.main === module) {
  testArbitragePerformance().catch(console.error);
}

module.exports = { testArbitragePerformance };
